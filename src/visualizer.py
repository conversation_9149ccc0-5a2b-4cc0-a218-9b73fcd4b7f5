"""
Visualization module for drawing bounding boxes and displaying OCR results.
"""
import logging
from typing import List, Dict, Tu<PERSON>, Optional, Union
from pathlib import Path

import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from PIL import Image, ImageDraw, ImageFont

from .ocr_processor import OCRResult

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class Visualizer:
    """Handle visualization of OCR results with bounding boxes and annotations."""
    
    def __init__(self, config: Dict):
        """
        Initialize visualizer with configuration.
        
        Args:
            config: Dictionary containing visualization configuration
        """
        self.config = config
        self.figure_size = config.get('figure_size', (12, 10))
        self.bbox_color = config.get('bbox_color', 'red')
        self.bbox_linewidth = config.get('bbox_linewidth', 2)
        self.text_color = config.get('text_color', 'red')
        self.text_fontsize = config.get('text_fontsize', 8)
        self.text_weight = config.get('text_weight', 'bold')
        self.text_bbox_style = config.get('text_bbox_style', {
            'boxstyle': 'round,pad=0.3',
            'facecolor': 'white',
            'alpha': 0.8
        })
    
    def visualize_results(self, image: Image.Image, ocr_result: OCRResult, 
                         confidence_threshold: float = 0.0, 
                         show_confidence: bool = True,
                         title: str = "OCR Results") -> plt.Figure:
        """
        Create a matplotlib visualization of OCR results.
        
        Args:
            image: PIL Image object
            ocr_result: OCRResult object containing detection results
            confidence_threshold: Minimum confidence to display
            show_confidence: Whether to show confidence scores in labels
            title: Title for the plot
            
        Returns:
            matplotlib Figure object
        """
        fig, ax = plt.subplots(1, 1, figsize=self.figure_size)
        ax.imshow(image)
        
        # Get formatted results above threshold
        results = ocr_result.get_formatted_results(confidence_threshold)
        
        for result in results:
            bbox = result['bbox']
            text = result['text']
            confidence = result['confidence']
            
            # Draw bounding box
            rect = patches.Rectangle(
                (bbox['x_min'], bbox['y_min']),
                bbox['width'], bbox['height'],
                linewidth=self.bbox_linewidth,
                edgecolor=self.bbox_color,
                facecolor='none'
            )
            ax.add_patch(rect)
            
            # Prepare label text
            if show_confidence:
                label = f"{text} ({confidence:.2f})"
            else:
                label = text
            
            # Add text annotation
            ax.text(
                bbox['x_min'], bbox['y_min'] - 5,
                label,
                fontsize=self.text_fontsize,
                color=self.text_color,
                weight=self.text_weight,
                bbox=self.text_bbox_style
            )
        
        ax.axis('off')
        ax.set_title(title, fontsize=16)
        plt.tight_layout()
        
        logger.info(f"Created visualization with {len(results)} text regions")
        return fig
    
    def create_annotated_image(self, image: Image.Image, ocr_result: OCRResult,
                              confidence_threshold: float = 0.0,
                              show_confidence: bool = True) -> Image.Image:
        """
        Create an annotated PIL Image with bounding boxes drawn directly on it.
        
        Args:
            image: PIL Image object
            ocr_result: OCRResult object containing detection results
            confidence_threshold: Minimum confidence to display
            show_confidence: Whether to show confidence scores in labels
            
        Returns:
            PIL Image with annotations
        """
        # Create a copy of the image to avoid modifying the original
        annotated_image = image.copy()
        draw = ImageDraw.Draw(annotated_image)
        
        # Try to load a font, fall back to default if not available
        try:
            font = ImageFont.truetype("arial.ttf", 16)
        except (OSError, IOError):
            font = ImageFont.load_default()
        
        # Get formatted results above threshold
        results = ocr_result.get_formatted_results(confidence_threshold)
        
        for result in results:
            bbox = result['bbox']
            text = result['text']
            confidence = result['confidence']
            polygon = result['polygon']
            
            # Draw bounding box using polygon for more accurate representation
            if len(polygon) >= 4:
                # Convert polygon to flat list of coordinates
                polygon_coords = [(int(point[0]), int(point[1])) for point in polygon]
                draw.polygon(polygon_coords, outline='red', width=3)
            else:
                # Fall back to rectangle
                draw.rectangle(
                    [bbox['x_min'], bbox['y_min'], bbox['x_max'], bbox['y_max']],
                    outline='red', width=3
                )
            
            # Prepare label text
            if show_confidence:
                label = f"{text} ({confidence:.2f})"
            else:
                label = text
            
            # Draw text with background
            text_bbox = draw.textbbox((bbox['x_min'], bbox['y_min'] - 25), label, font=font)
            draw.rectangle(text_bbox, fill='white', outline='red')
            draw.text((bbox['x_min'], bbox['y_min'] - 25), label, fill='red', font=font)
        
        logger.info(f"Created annotated image with {len(results)} text regions")
        return annotated_image
    
    def create_results_summary_plot(self, results_dict: Dict[str, OCRResult],
                                   confidence_threshold: float = 0.0) -> plt.Figure:
        """
        Create a summary plot showing statistics across multiple images.
        
        Args:
            results_dict: Dictionary mapping image paths to OCRResult objects
            confidence_threshold: Minimum confidence to include in statistics
            
        Returns:
            matplotlib Figure object with summary plots
        """
        # Collect statistics
        image_names = []
        text_counts = []
        avg_confidences = []
        
        for image_path, ocr_result in results_dict.items():
            results = ocr_result.get_formatted_results(confidence_threshold)
            if results:
                image_names.append(Path(image_path).name)
                text_counts.append(len(results))
                avg_confidences.append(np.mean([r['confidence'] for r in results]))
            else:
                image_names.append(Path(image_path).name)
                text_counts.append(0)
                avg_confidences.append(0)
        
        # Create subplots
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))
        
        # Plot 1: Text count per image
        bars1 = ax1.bar(range(len(image_names)), text_counts, color='skyblue', alpha=0.7)
        ax1.set_xlabel('Images')
        ax1.set_ylabel('Number of Text Regions')
        ax1.set_title('Text Regions Detected per Image')
        ax1.set_xticks(range(len(image_names)))
        ax1.set_xticklabels(image_names, rotation=45, ha='right')
        
        # Add value labels on bars
        for bar, count in zip(bars1, text_counts):
            if count > 0:
                ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.1,
                        str(count), ha='center', va='bottom')
        
        # Plot 2: Average confidence per image
        bars2 = ax2.bar(range(len(image_names)), avg_confidences, color='lightcoral', alpha=0.7)
        ax2.set_xlabel('Images')
        ax2.set_ylabel('Average Confidence')
        ax2.set_title('Average Confidence Score per Image')
        ax2.set_xticks(range(len(image_names)))
        ax2.set_xticklabels(image_names, rotation=45, ha='right')
        ax2.set_ylim(0, 1)
        
        # Add value labels on bars
        for bar, conf in zip(bars2, avg_confidences):
            if conf > 0:
                ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                        f'{conf:.2f}', ha='center', va='bottom')
        
        plt.tight_layout()
        logger.info(f"Created summary plot for {len(results_dict)} images")
        return fig
    
    def display_results_grid(self, images_and_results: List[Tuple[Image.Image, OCRResult, str]],
                           confidence_threshold: float = 0.0,
                           max_cols: int = 3) -> plt.Figure:
        """
        Display multiple images with their OCR results in a grid layout.
        
        Args:
            images_and_results: List of tuples (image, ocr_result, title)
            confidence_threshold: Minimum confidence to display
            max_cols: Maximum number of columns in the grid
            
        Returns:
            matplotlib Figure object
        """
        n_images = len(images_and_results)
        if n_images == 0:
            logger.warning("No images provided for grid display")
            return plt.figure()
        
        # Calculate grid dimensions
        cols = min(max_cols, n_images)
        rows = (n_images + cols - 1) // cols
        
        fig, axes = plt.subplots(rows, cols, figsize=(5 * cols, 5 * rows))
        
        # Handle single image case
        if n_images == 1:
            axes = [axes]
        elif rows == 1:
            axes = [axes] if cols == 1 else axes
        else:
            axes = axes.flatten()
        
        for i, (image, ocr_result, title) in enumerate(images_and_results):
            ax = axes[i] if isinstance(axes, list) else axes
            ax.imshow(image)
            
            # Get and draw results
            results = ocr_result.get_formatted_results(confidence_threshold)
            for result in results:
                bbox = result['bbox']
                rect = patches.Rectangle(
                    (bbox['x_min'], bbox['y_min']),
                    bbox['width'], bbox['height'],
                    linewidth=1, edgecolor='red', facecolor='none'
                )
                ax.add_patch(rect)
            
            ax.set_title(f"{title}\n({len(results)} regions)", fontsize=10)
            ax.axis('off')
        
        # Hide unused subplots
        for i in range(n_images, len(axes)):
            axes[i].axis('off')
        
        plt.tight_layout()
        logger.info(f"Created grid display with {n_images} images")
        return fig
