"""
OCR processing module using PaddleOCR for text detection and recognition.
"""
import logging
from typing import List, Dict, Optional, Union, Tuple
from pathlib import Path

import numpy as np
from PIL import Image
from paddleocr import PaddleOCR

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class OCRResult:
    """Container for OCR results with convenient access methods."""
    
    def __init__(self, raw_result: Dict):
        self.raw_result = raw_result
        self._parse_result()
    
    def _parse_result(self):
        """Parse the raw PaddleOCR result into convenient attributes."""
        if self.raw_result:
            self.texts = self.raw_result.get('rec_texts', [])
            self.scores = self.raw_result.get('rec_scores', [])
            self.boxes = self.raw_result.get('rec_polys', [])
            self.input_path = self.raw_result.get('input_path', '')
        else:
            self.texts = []
            self.scores = []
            self.boxes = []
            self.input_path = ''
    
    def get_formatted_results(self, confidence_threshold: float = 0.0) -> List[Dict]:
        """Get results formatted as a list of dictionaries."""
        results = []
        for i, (text, score, box) in enumerate(zip(self.texts, self.scores, self.boxes)):
            if score >= confidence_threshold:
                # Convert box coordinates to more convenient format
                box_array = np.array(box)
                x_min, y_min = box_array[:, 0].min(), box_array[:, 1].min()
                x_max, y_max = box_array[:, 0].max(), box_array[:, 1].max()
                
                results.append({
                    'id': i,
                    'text': text,
                    'confidence': score,
                    'bbox': {
                        'x_min': int(x_min),
                        'y_min': int(y_min),
                        'x_max': int(x_max),
                        'y_max': int(y_max),
                        'width': int(x_max - x_min),
                        'height': int(y_max - y_min)
                    },
                    'polygon': box.tolist() if hasattr(box, 'tolist') else box
                })
        return results
    
    def get_text_only(self, confidence_threshold: float = 0.0) -> List[str]:
        """Get only the text content above confidence threshold."""
        return [text for text, score in zip(self.texts, self.scores) 
                if score >= confidence_threshold]
    
    def get_full_text(self, confidence_threshold: float = 0.0, separator: str = ' ') -> str:
        """Get all text concatenated into a single string."""
        texts = self.get_text_only(confidence_threshold)
        return separator.join(texts)
    
    def has_results(self) -> bool:
        """Check if any text was detected."""
        return len(self.texts) > 0


class OCRProcessor:
    """Main OCR processing class using PaddleOCR."""
    
    def __init__(self, config: Dict):
        """
        Initialize OCR processor with configuration.
        
        Args:
            config: Dictionary containing OCR configuration parameters
        """
        self.config = config
        self.ocr_engine = None
        self._initialize_ocr()
    
    def _initialize_ocr(self):
        """Initialize the PaddleOCR engine."""
        try:
            logger.info("Initializing PaddleOCR engine...")
            self.ocr_engine = PaddleOCR(**self.config)
            logger.info("PaddleOCR engine initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize PaddleOCR: {str(e)}")
            raise
    
    def process_image(self, image_path: Union[str, Path]) -> OCRResult:
        """
        Process a single image and return OCR results.
        
        Args:
            image_path: Path to the image file
            
        Returns:
            OCRResult object containing the processed results
        """
        try:
            image_path = str(image_path)
            logger.info(f"Processing image: {image_path}")
            
            # Perform OCR
            raw_results = self.ocr_engine.predict(image_path)
            
            if raw_results and len(raw_results) > 0:
                result = OCRResult(raw_results[0])
                logger.info(f"OCR completed. Found {len(result.texts)} text regions")
                return result
            else:
                logger.warning(f"No text detected in image: {image_path}")
                return OCRResult({})
                
        except Exception as e:
            logger.error(f"Error processing image {image_path}: {str(e)}")
            raise
    
    def process_multiple_images(self, image_paths: List[Union[str, Path]]) -> Dict[str, OCRResult]:
        """
        Process multiple images and return results.
        
        Args:
            image_paths: List of paths to image files
            
        Returns:
            Dictionary mapping image paths to OCRResult objects
        """
        results = {}
        total_images = len(image_paths)
        
        logger.info(f"Starting batch processing of {total_images} images")
        
        for i, image_path in enumerate(image_paths, 1):
            try:
                logger.info(f"Processing image {i}/{total_images}: {image_path}")
                result = self.process_image(image_path)
                results[str(image_path)] = result
            except Exception as e:
                logger.error(f"Failed to process image {image_path}: {str(e)}")
                results[str(image_path)] = OCRResult({})
        
        logger.info(f"Batch processing completed. Processed {len(results)} images")
        return results
    
    def get_engine_info(self) -> Dict:
        """Get information about the OCR engine configuration."""
        return {
            "engine": "PaddleOCR",
            "config": self.config,
            "initialized": self.ocr_engine is not None
        }


class OCRBatchProcessor:
    """Handle batch processing of multiple images with progress tracking."""
    
    def __init__(self, ocr_processor: OCRProcessor):
        self.ocr_processor = ocr_processor
        self.results = {}
        self.failed_images = []
    
    def process_directory(self, directory_path: Union[str, Path], 
                         image_handler) -> Dict[str, OCRResult]:
        """
        Process all images in a directory.
        
        Args:
            directory_path: Path to directory containing images
            image_handler: ImageHandler instance for file validation
            
        Returns:
            Dictionary mapping image paths to OCRResult objects
        """
        directory_path = Path(directory_path)
        image_files = image_handler.get_images_from_directory(directory_path)
        
        if not image_files:
            logger.warning(f"No valid images found in directory: {directory_path}")
            return {}
        
        return self.ocr_processor.process_multiple_images(image_files)
    
    def get_processing_summary(self) -> Dict:
        """Get a summary of the batch processing results."""
        total_processed = len(self.results)
        successful = sum(1 for result in self.results.values() if result.has_results())
        failed = total_processed - successful
        
        return {
            "total_processed": total_processed,
            "successful": successful,
            "failed": failed,
            "success_rate": (successful / total_processed * 100) if total_processed > 0 else 0
        }
