"""
Utility functions for image handling, file operations, and helper functions.
"""
import os
import json
import csv
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Tuple, Optional, Union
import logging

from PIL import Image
import numpy as np

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ImageHandler:
    """Handle image loading, validation, and basic operations."""
    
    def __init__(self, supported_formats: List[str]):
        self.supported_formats = [fmt.lower() for fmt in supported_formats]
    
    def is_valid_image(self, file_path: Union[str, Path]) -> bool:
        """Check if file is a valid image format."""
        file_path = Path(file_path)
        return file_path.suffix.lower() in self.supported_formats
    
    def load_image(self, image_path: Union[str, Path]) -> Optional[Image.Image]:
        """Load and validate an image."""
        try:
            image_path = Path(image_path)
            if not image_path.exists():
                logger.error(f"Image file not found: {image_path}")
                return None
            
            if not self.is_valid_image(image_path):
                logger.error(f"Unsupported image format: {image_path.suffix}")
                return None
            
            image = Image.open(image_path).convert('RGB')
            logger.info(f"Successfully loaded image: {image_path}")
            return image
            
        except Exception as e:
            logger.error(f"Error loading image {image_path}: {str(e)}")
            return None
    
    def resize_image_if_needed(self, image: Image.Image, max_size: Tuple[int, int]) -> Image.Image:
        """Resize image if it exceeds maximum dimensions."""
        if image.size[0] > max_size[0] or image.size[1] > max_size[1]:
            image.thumbnail(max_size, Image.Resampling.LANCZOS)
            logger.info(f"Image resized to: {image.size}")
        return image
    
    def get_images_from_directory(self, directory: Union[str, Path]) -> List[Path]:
        """Get all valid image files from a directory."""
        directory = Path(directory)
        if not directory.exists():
            logger.error(f"Directory not found: {directory}")
            return []
        
        image_files = []
        for file_path in directory.iterdir():
            if file_path.is_file() and self.is_valid_image(file_path):
                image_files.append(file_path)
        
        logger.info(f"Found {len(image_files)} image files in {directory}")
        return sorted(image_files)


class FileManager:
    """Handle file operations and output management."""
    
    def __init__(self, output_dir: Union[str, Path], create_timestamp_folders: bool = True):
        self.output_dir = Path(output_dir)
        self.create_timestamp_folders = create_timestamp_folders
        self.current_session_dir = None
        
        if create_timestamp_folders:
            self.current_session_dir = self._create_session_directory()
    
    def _create_session_directory(self) -> Path:
        """Create a timestamped directory for the current session."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        session_dir = self.output_dir / f"session_{timestamp}"
        session_dir.mkdir(parents=True, exist_ok=True)
        logger.info(f"Created session directory: {session_dir}")
        return session_dir
    
    def get_output_directory(self) -> Path:
        """Get the appropriate output directory."""
        return self.current_session_dir if self.current_session_dir else self.output_dir
    
    def save_image(self, image: Image.Image, filename: str, quality: int = 95) -> Path:
        """Save an image to the output directory."""
        output_dir = self.get_output_directory()
        output_path = output_dir / filename
        
        try:
            image.save(output_path, quality=quality, optimize=True)
            logger.info(f"Image saved: {output_path}")
            return output_path
        except Exception as e:
            logger.error(f"Error saving image {output_path}: {str(e)}")
            raise
    
    def save_text_results(self, results: List[Dict], filename: str, format_type: str = "txt") -> Path:
        """Save OCR results in specified format."""
        output_dir = self.get_output_directory()
        
        if format_type.lower() == "json":
            output_path = output_dir / f"{filename}.json"
            self._save_json(results, output_path)
        elif format_type.lower() == "csv":
            output_path = output_dir / f"{filename}.csv"
            self._save_csv(results, output_path)
        else:  # Default to txt
            output_path = output_dir / f"{filename}.txt"
            self._save_txt(results, output_path)
        
        return output_path
    
    def _save_json(self, results: List[Dict], output_path: Path):
        """Save results as JSON."""
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            logger.info(f"JSON results saved: {output_path}")
        except Exception as e:
            logger.error(f"Error saving JSON {output_path}: {str(e)}")
            raise
    
    def _save_csv(self, results: List[Dict], output_path: Path):
        """Save results as CSV."""
        try:
            if not results:
                return
            
            with open(output_path, 'w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=results[0].keys())
                writer.writeheader()
                writer.writerows(results)
            logger.info(f"CSV results saved: {output_path}")
        except Exception as e:
            logger.error(f"Error saving CSV {output_path}: {str(e)}")
            raise
    
    def _save_txt(self, results: List[Dict], output_path: Path):
        """Save results as plain text."""
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                for result in results:
                    f.write(f"Text: {result.get('text', '')} | ")
                    f.write(f"Confidence: {result.get('confidence', 0):.2f}\n")
            logger.info(f"Text results saved: {output_path}")
        except Exception as e:
            logger.error(f"Error saving text {output_path}: {str(e)}")
            raise


def format_confidence(confidence: float) -> str:
    """Format confidence score as percentage."""
    return f"{confidence * 100:.1f}%"


def filter_results_by_confidence(results: List[Dict], threshold: float) -> List[Dict]:
    """Filter OCR results by confidence threshold."""
    filtered = [r for r in results if r.get('confidence', 0) >= threshold]
    logger.info(f"Filtered {len(results)} results to {len(filtered)} above {threshold} confidence")
    return filtered


def create_result_summary(results: List[Dict]) -> Dict:
    """Create a summary of OCR results."""
    if not results:
        return {"total_texts": 0, "avg_confidence": 0, "high_confidence_count": 0}
    
    confidences = [r.get('confidence', 0) for r in results]
    high_confidence_count = sum(1 for c in confidences if c >= 0.8)
    
    return {
        "total_texts": len(results),
        "avg_confidence": sum(confidences) / len(confidences),
        "high_confidence_count": high_confidence_count,
        "min_confidence": min(confidences),
        "max_confidence": max(confidences)
    }
