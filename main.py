from paddleocr import PaddleOCR, draw_ocr
from PIL import Image
import matplotlib.pyplot as plt

# Khởi tạo OCR - hỗ trợ tiếng Việt, dùng CPU
ocr = PaddleOCR(use_angle_cls=True, lang='vi')  # lang='en' nếu muốn tiếng Anh

# Đường dẫn ảnh (ảnh phải nằm cùng folder hoặc sửa lại path)
image_path = 'image.png'

# Nhận diện văn bản trong ảnh
result = ocr.ocr(image_path, cls=True)

# In kết quả ra terminal
for line in result:
    for word_info in line:
        text = word_info[1][0]
        confidence = word_info[1][1]
        print(f"Text: {text} | Confidence: {confidence:.2f}")

# Mở ảnh và chuẩn bị vẽ kết quả
image = Image.open(image_path).convert('RGB')
boxes = [word_info[0] for line in result for word_info in line]
texts = [word_info[1][0] for line in result for word_info in line]
scores = [word_info[1][1] for line in result for word_info in line]

# Vẽ kết quả nhận dạng lên ảnh
visualized_img = draw_ocr(image, boxes, texts, scores, font_path='Arial.ttf')  # Nếu dùng tiếng Việt nên có font hỗ trợ Unicode
visualized_img = Image.fromarray(visualized_img)

# Hiển thị ảnh kết quả lên màn hình
plt.figure(figsize=(12, 10))
plt.imshow(visualized_img)
plt.axis('off')
plt.title("Kết quả nhận diện văn bản")
plt.show()