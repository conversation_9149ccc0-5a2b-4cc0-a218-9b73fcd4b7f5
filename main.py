#!/usr/bin/env python3
"""
Main application entry point for the OCR system.
Orchestrates all modules and handles multiple image processing.
"""
import sys
import logging
from pathlib import Path
import argparse
from typing import Optional

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from config.settings import get_config, ensure_directories
from src.utils import ImageHandler, FileManager, filter_results_by_confidence, create_result_summary
from src.ocr_processor import OCRProcessor, OCRBatchProcessor
from src.visualizer import Visualizer

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class OCRApplication:
    """Main application class that orchestrates the OCR workflow."""

    def __init__(self, config_override: Optional[dict] = None):
        """Initialize the OCR application with configuration."""
        self.config = get_config()
        if config_override:
            # Deep merge configuration
            for key, value in config_override.items():
                if key in self.config and isinstance(self.config[key], dict) and isinstance(value, dict):
                    self.config[key].update(value)
                else:
                    self.config[key] = value

        # Ensure directories exist
        ensure_directories()

        # Initialize components
        self.image_handler = ImageHandler(self.config['image']['supported_formats'])
        self.file_manager = FileManager(
            self.config['paths']['output_dir'],
            self.config.get('output', {}).get('create_timestamp_folders', True)
        )
        self.ocr_processor = OCRProcessor(self.config['ocr'])
        self.visualizer = Visualizer(self.config['visualization'])

        logger.info("OCR Application initialized successfully")

    def process_single_image(self, image_path: Path, show_visualization: bool = True,
                           save_results: bool = True) -> dict:
        """
        Process a single image and return results.

        Args:
            image_path: Path to the image file
            show_visualization: Whether to display the visualization
            save_results: Whether to save results to files

        Returns:
            Dictionary containing processing results and metadata
        """
        logger.info(f"Processing single image: {image_path}")

        # Load and validate image
        image = self.image_handler.load_image(image_path)
        if image is None:
            return {"success": False, "error": "Failed to load image"}

        # Resize if needed
        image = self.image_handler.resize_image_if_needed(
            image, self.config['image']['max_image_size']
        )

        # Perform OCR
        try:
            ocr_result = self.ocr_processor.process_image(image_path)

            # Filter results by confidence threshold
            confidence_threshold = self.config['output']['confidence_threshold']
            formatted_results = ocr_result.get_formatted_results(confidence_threshold)
            filtered_results = filter_results_by_confidence(formatted_results, confidence_threshold)

            # Print results to console
            self._print_results(filtered_results, image_path.name)

            # Create visualization
            if show_visualization:
                fig = self.visualizer.visualize_results(
                    image, ocr_result, confidence_threshold,
                    title=f"OCR Results: {image_path.name}"
                )
                fig.show()

            # Save results if requested
            if save_results:
                self._save_results(image, ocr_result, image_path, filtered_results)

            return {
                "success": True,
                "image_path": str(image_path),
                "text_regions_found": len(filtered_results),
                "results": filtered_results,
                "summary": create_result_summary(filtered_results)
            }

        except Exception as e:
            logger.error(f"Error processing image {image_path}: {str(e)}")
            return {"success": False, "error": str(e)}

    def process_directory(self, directory_path: Path, show_visualization: bool = True,
                         save_results: bool = True) -> dict:
        """
        Process all images in a directory.

        Args:
            directory_path: Path to directory containing images
            show_visualization: Whether to display visualizations
            save_results: Whether to save results to files

        Returns:
            Dictionary containing batch processing results
        """
        logger.info(f"Processing directory: {directory_path}")

        # Get all image files
        image_files = self.image_handler.get_images_from_directory(directory_path)
        if not image_files:
            return {"success": False, "error": "No valid images found in directory"}

        # Process images
        batch_processor = OCRBatchProcessor(self.ocr_processor)
        results_dict = batch_processor.process_directory(directory_path, self.image_handler)

        # Process and save results
        all_results = []
        images_and_results = []

        confidence_threshold = self.config['output']['confidence_threshold']

        for image_path, ocr_result in results_dict.items():
            image_path_obj = Path(image_path)

            # Load image for visualization
            image = self.image_handler.load_image(image_path_obj)
            if image is None:
                continue

            # Get filtered results
            formatted_results = ocr_result.get_formatted_results(confidence_threshold)
            filtered_results = filter_results_by_confidence(formatted_results, confidence_threshold)
            all_results.extend(filtered_results)

            # Collect for grid visualization
            images_and_results.append((image, ocr_result, image_path_obj.name))

            # Print individual results
            self._print_results(filtered_results, image_path_obj.name)

            # Save individual results
            if save_results:
                self._save_results(image, ocr_result, image_path_obj, filtered_results)

        # Create summary visualization
        if show_visualization and results_dict:
            # Show grid of all images
            if len(images_and_results) <= 9:  # Limit grid size
                grid_fig = self.visualizer.display_results_grid(images_and_results)
                grid_fig.show()

            # Show summary statistics
            summary_fig = self.visualizer.create_results_summary_plot(results_dict, confidence_threshold)
            summary_fig.show()

        # Create batch summary
        processing_summary = batch_processor.get_processing_summary()
        results_summary = create_result_summary(all_results)

        return {
            "success": True,
            "directory_path": str(directory_path),
            "images_processed": len(results_dict),
            "total_text_regions": len(all_results),
            "processing_summary": processing_summary,
            "results_summary": results_summary
        }

    def _print_results(self, results: list, image_name: str):
        """Print OCR results to console."""
        print(f"\n=== OCR RESULTS FOR {image_name.upper()} ===")
        if results:
            for i, result in enumerate(results, 1):
                print(f"{i:2d}. Text: {result['text']} | Confidence: {result['confidence']:.2f}")
        else:
            print("No text detected above confidence threshold.")
        print("-" * 50)

    def _save_results(self, image, ocr_result, image_path, filtered_results):
        """Save OCR results and annotated images."""
        base_name = image_path.stem

        # Save annotated image
        if self.config['output']['save_annotated_images']:
            annotated_image = self.visualizer.create_annotated_image(image, ocr_result)
            self.file_manager.save_image(
                annotated_image,
                f"{base_name}_annotated.{self.config['image']['output_format']}",
                self.config['image']['quality']
            )

        # Save text results
        if self.config['output']['save_text_results']:
            self.file_manager.save_text_results(
                filtered_results,
                f"{base_name}_results",
                self.config['output']['text_output_format']
            )


def main():
    """Main entry point with command line argument parsing."""
    parser = argparse.ArgumentParser(description="OCR Application for text detection and recognition")
    parser.add_argument("input", nargs="?", help="Input image file or directory path")
    parser.add_argument("--no-display", action="store_true", help="Don't show visualizations")
    parser.add_argument("--no-save", action="store_true", help="Don't save results to files")
    parser.add_argument("--confidence", type=float, default=0.5,
                       help="Minimum confidence threshold (0.0-1.0)")
    parser.add_argument("--lang", default="vi", help="OCR language (vi for Vietnamese, en for English)")

    args = parser.parse_args()

    # Configuration override based on arguments
    config_override = {
        'output': {'confidence_threshold': args.confidence},
        'ocr': {'lang': args.lang}
    }

    # Initialize application
    app = OCRApplication(config_override)

    # Determine input path
    if args.input:
        input_path = Path(args.input)
    else:
        # Default to images directory
        input_path = app.config['paths']['images_dir']

    if not input_path.exists():
        logger.error(f"Input path does not exist: {input_path}")
        return 1

    # Process input
    try:
        if input_path.is_file():
            result = app.process_single_image(
                input_path,
                show_visualization=not args.no_display,
                save_results=not args.no_save
            )
        else:
            result = app.process_directory(
                input_path,
                show_visualization=not args.no_display,
                save_results=not args.no_save
            )

        if result["success"]:
            logger.info("Processing completed successfully")
            return 0
        else:
            logger.error(f"Processing failed: {result.get('error', 'Unknown error')}")
            return 1

    except KeyboardInterrupt:
        logger.info("Processing interrupted by user")
        return 1
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        return 1


if __name__ == "__main__":
    exit(main())