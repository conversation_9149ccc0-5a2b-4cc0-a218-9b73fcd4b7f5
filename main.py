from paddleocr import PaddleOCR
from PIL import Image
import matplotlib.pyplot as plt
import matplotlib.patches as patches
import numpy as np

ocr = PaddleOCR(use_textline_orientation=True, lang='vi') 

image_path = 'image.png'

result = ocr.predict(image_path)

print("=== KẾT QUẢ NHẬN DIỆN VẤN BẢN ===")

if result and len(result) > 0:
    ocr_result = result[0] 

    texts = ocr_result['rec_texts']
    scores = ocr_result['rec_scores']
    boxes = ocr_result['rec_polys']

    for i, (text, score) in enumerate(zip(texts, scores)):
        print(f"Text: {text} | Confidence: {score:.2f}")

    image = Image.open(image_path).convert('RGB')
    fig, ax = plt.subplots(1, 1, figsize=(12, 10))
    ax.imshow(image)

    for i, (box, text, score) in enumerate(zip(boxes, texts, scores)):
        box = np.array(box)
        x_min, y_min = box[:, 0].min(), box[:, 1].min()
        x_max, y_max = box[:, 0].max(), box[:, 1].max()

        rect = patches.Rectangle((x_min, y_min), x_max - x_min, y_max - y_min,
                               linewidth=2, edgecolor='red', facecolor='none')
        ax.add_patch(rect)

        ax.text(x_min, y_min - 5, f"{text} ({score:.2f})",
                fontsize=8, color='red', weight='bold',
                bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8))

    ax.axis('off')
    ax.set_title("Kết quả nhận diện văn bản", fontsize=16)
    plt.tight_layout()
    plt.show()

else:
    print("No text detected in the image.")

ax.axis('off')
ax.set_title("Kết quả nhận diện văn bản", fontsize=16)
plt.tight_layout()
plt.show()