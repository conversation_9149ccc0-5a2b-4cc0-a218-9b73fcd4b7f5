from paddleocr import PaddleOCR
from PIL import Image
import matplotlib.pyplot as plt
import matplotlib.patches as patches
import numpy as np

# Khởi tạo OCR - hỗ trợ tiếng Việt, dùng CPU
ocr = PaddleOCR(use_textline_orientation=True, lang='vi')  # lang='en' nếu muốn tiếng Anh

# Đường dẫn ảnh (ảnh phải nằm cùng folder hoặc sửa lại path)
image_path = 'image.png'

# Nhận diện văn bản trong ảnh
result = ocr.predict(image_path)

# In kết quả ra terminal
print("=== KẾT QUẢ NHẬN DIỆN VẤN BẢN ===")

# Xử lý kết quả với cấu trúc mới của PaddleOCR 3.x
if result and len(result) > 0:
    ocr_result = result[0]  # Lấy kết quả đầu tiên
    
    # Lấy texts và scores từ kết quả
    texts = ocr_result['rec_texts']
    scores = ocr_result['rec_scores']
    boxes = ocr_result['rec_polys']
    
    # In kết quả
    for i, (text, score) in enumerate(zip(texts, scores), 1):
        print(f"Text: {text} | Confidence: {score:.2f}")
    
    # Hiển thị ảnh với bounding boxes
    image = Image.open(image_path).convert('RGB')
    fig, ax = plt.subplots(1, 1, figsize=(12, 10))
    ax.imshow(image)
    
    # Vẽ bounding boxes và text
    for i, (box, text, score) in enumerate(zip(boxes, texts, scores), 1):
        # Chuyển đổi box thành rectangle
        box = np.array(box)
        x_min, y_min = box[:, 0].min(), box[:, 1].min()
        x_max, y_max = box[:, 0].max(), box[:, 1].max()
        
        # Vẽ rectangle
        rect = patches.Rectangle((x_min, y_min), x_max - x_min, y_max - y_min,
                               linewidth=2, edgecolor='red', facecolor='none')
        ax.add_patch(rect)
        
        # Thêm text
        ax.text(x_min, y_min - 5, f"{text} ({score:.2f})", 
                fontsize=8, color='red', weight='bold',
                bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8))
    
    ax.axis('off')
    ax.set_title("Kết quả nhận diện văn bản", fontsize=16)
    plt.tight_layout()
    plt.show()
    
else:
    print("No text detected in the image.")
