from paddleocr import PaddleOCR
from PIL import Image
import matplotlib.pyplot as plt
import numpy as np

# Khởi tạo OCR - hỗ trợ tiếng Việt, dùng CPU
ocr = PaddleOCR(use_textline_orientation=True, lang='vi')  # lang='en' nếu muốn tiếng Anh

# Đường dẫn ảnh (ảnh phải nằm cùng folder hoặc sửa lại path)
image_path = 'image.png'

# Nhận diện văn bản trong ảnh
result = ocr.predict(image_path)

# In kết quả ra terminal
print("=== KẾT QUẢ NHẬN DIỆN VẤN BẢN ===")

# Xử lý kết quả với cấu trúc mới của PaddleOCR 3.x
if result and len(result) > 0:
    ocr_result = result[0]  # Lấy kết quả đầu tiên

    # Lấy texts và scores từ kết quả
    texts = ocr_result['rec_texts']
    scores = ocr_result['rec_scores']
    boxes = ocr_result['rec_polys']

    # Hiển thị ảnh gốc và kết quả text riêng biệt
    image = Image.open(image_path).convert('RGB')

    # Tạo layout với 2 phần: ảnh và kết quả text
    fig = plt.figure(figsize=(16, 10))

    # Phần 1: Hiển thị ảnh gốc với marker nhỏ gọn
    ax1 = plt.subplot(1, 2, 1)
    ax1.imshow(image)
    ax1.axis('off')
    ax1.set_title("Ảnh gốc", fontsize=14, fontweight='bold')

    # Thêm marker nhỏ gọn cho từng vùng text
    for i, (box, text, score) in enumerate(zip(boxes, texts, scores), 1):
        # Chuyển đổi box thành tọa độ
        box = np.array(box)
        x_center = box[:, 0].mean()
        y_center = box[:, 1].mean()

        # Vẽ marker nhỏ, tinh tế với viền
        circle = plt.Circle((x_center, y_center), radius=6,
                          color='darkblue', alpha=0.8, zorder=10)
        ax1.add_patch(circle)

        # Thêm viền trắng mỏng để marker nổi bật trên mọi nền
        circle_border = plt.Circle((x_center, y_center), radius=7,
                                 color='white', alpha=0.9, zorder=9, fill=False, linewidth=1.5)
        ax1.add_patch(circle_border)

        # Thêm số thứ tự nhỏ gọn
        ax1.text(x_center, y_center, str(i),
                fontsize=8, fontweight='bold', color='white',
                ha='center', va='center', zorder=11)

    # Phần 2: Hiển thị kết quả text dưới dạng danh sách
    ax2 = plt.subplot(1, 2, 2)
    ax2.axis('off')
    ax2.set_title("Kết quả nhận diện văn bản", fontsize=14, fontweight='bold')

    # Tạo text hiển thị kết quả với số thứ tự nổi bật
    result_text = "DANH SÁCH VẤN BẢN ĐƯỢC NHẬN DIỆN:\n"
    result_text += "(Số thứ tự tương ứng với marker nhỏ trên ảnh)\n\n"
    for i, (text, score) in enumerate(zip(texts, scores), 1):
        confidence_percent = score * 100
        result_text += f"[{i:2d}] {text}\n"
        result_text += f"     Độ tin cậy: {confidence_percent:.1f}%\n\n"

    # Thêm thống kê tổng quan
    avg_confidence = sum(scores) / len(scores) * 100
    high_confidence_count = sum(1 for score in scores if score >= 0.9)

    result_text += f"{'='*40}\n"
    result_text += f"THỐNG KÊ:\n"
    result_text += f"• Tổng số vùng text: {len(texts)}\n"
    result_text += f"• Độ tin cậy trung bình: {avg_confidence:.1f}%\n"
    result_text += f"• Số vùng độ tin cậy cao (≥90%): {high_confidence_count}\n"

    # Hiển thị text với font và màu sắc đẹp (sử dụng font mặc định để tránh lỗi Vietnamese)
    ax2.text(0.05, 0.95, result_text, transform=ax2.transAxes,
             fontsize=11, verticalalignment='top', fontfamily='sans-serif',
             bbox=dict(boxstyle="round,pad=0.5", facecolor="lightblue", alpha=0.8))

    plt.tight_layout()

    # Lưu hình ảnh kết quả thay vì hiển thị (để tránh treo trong terminal)
    output_filename = 'ocr_results.png'
    plt.savefig(output_filename, dpi=300, bbox_inches='tight')
    print(f"\n📸 Hình ảnh kết quả đã được lưu: {output_filename}")

    # Uncomment dòng dưới nếu muốn hiển thị trực tiếp (chỉ khi có GUI)
    # plt.show()

    # In kết quả ra terminal (giữ nguyên để dễ copy)
    print("\n" + "="*60)
    print("CHI TIẾT KẾT QUẢ NHẬN DIỆN:")
    print("(Số thứ tự tương ứng với marker nhỏ trên ảnh)")
    print("="*60)
    for i, (text, score) in enumerate(zip(texts, scores), 1):
        print(f"[{i:2d}] Text: {text}")
        print(f"      Confidence: {score:.3f} ({score*100:.1f}%)")
        print("-" * 50)

else:
    print("No text detected in the image.")
