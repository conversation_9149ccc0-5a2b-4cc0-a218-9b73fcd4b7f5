#!/usr/bin/env python3
"""
Example usage of the restructured OCR system.
This demonstrates how to use the modular OCR application.
"""
import sys
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from main import OCRApplication


def example_single_image():
    """Example: Process a single image."""
    print("=== EXAMPLE: Processing Single Image ===")
    
    # Initialize the OCR application
    app = OCRApplication()
    
    # Process the image in the images directory
    image_path = Path("images/image.png")
    
    if image_path.exists():
        result = app.process_single_image(
            image_path,
            show_visualization=True,
            save_results=True
        )
        
        if result["success"]:
            print(f"✅ Successfully processed {result['image_path']}")
            print(f"📊 Found {result['text_regions_found']} text regions")
            print(f"📈 Summary: {result['summary']}")
        else:
            print(f"❌ Failed to process image: {result['error']}")
    else:
        print(f"❌ Image not found: {image_path}")


def example_directory_processing():
    """Example: Process all images in a directory."""
    print("\n=== EXAMPLE: Processing Directory ===")
    
    # Initialize the OCR application with custom settings
    config_override = {
        'output': {'confidence_threshold': 0.7},  # Higher confidence threshold
        'ocr': {'lang': 'vi'}  # Vietnamese language
    }
    
    app = OCRApplication(config_override)
    
    # Process all images in the images directory
    images_dir = Path("images")
    
    if images_dir.exists():
        result = app.process_directory(
            images_dir,
            show_visualization=True,
            save_results=True
        )
        
        if result["success"]:
            print(f"✅ Successfully processed {result['images_processed']} images")
            print(f"📊 Total text regions found: {result['total_text_regions']}")
            print(f"📈 Processing summary: {result['processing_summary']}")
            print(f"📋 Results summary: {result['results_summary']}")
        else:
            print(f"❌ Failed to process directory: {result['error']}")
    else:
        print(f"❌ Directory not found: {images_dir}")


def example_custom_configuration():
    """Example: Using custom configuration."""
    print("\n=== EXAMPLE: Custom Configuration ===")
    
    # Custom configuration for English OCR with different visualization settings
    custom_config = {
        'ocr': {
            'lang': 'en',  # English language
            'use_textline_orientation': True
        },
        'visualization': {
            'bbox_color': 'blue',
            'text_color': 'blue',
            'figure_size': (15, 12)
        },
        'output': {
            'confidence_threshold': 0.8,  # High confidence only
            'text_output_format': 'json'  # Save as JSON
        }
    }
    
    app = OCRApplication(custom_config)
    
    # Process with custom settings
    image_path = Path("images/image.png")
    if image_path.exists():
        result = app.process_single_image(image_path, show_visualization=False, save_results=True)
        print(f"Custom processing result: {result['success']}")


if __name__ == "__main__":
    print("🚀 OCR System Examples")
    print("=" * 50)
    
    try:
        # Run examples
        example_single_image()
        example_directory_processing()
        example_custom_configuration()
        
        print("\n✅ All examples completed!")
        print("\n📁 Check the 'output' directory for saved results.")
        print("🖼️ Annotated images and text files should be saved there.")
        
    except Exception as e:
        print(f"\n❌ Error running examples: {str(e)}")
        import traceback
        traceback.print_exc()
