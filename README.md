# OCR Application - Restructured

A modular OCR (Optical Character Recognition) application built with PaddleOCR, featuring Vietnamese language support and comprehensive text detection capabilities.

## 🏗️ Project Structure

```
paddle_ocr/
├── main.py                 # Main application entry point
├── example_usage.py        # Usage examples
├── README.md              # This file
├── test.py                # Original test file (legacy)
├── config/
│   └── settings.py        # Configuration management
├── src/
│   ├── __init__.py        # Package initialization
│   ├── ocr_processor.py   # OCR processing module
│   ├── visualizer.py      # Visualization module
│   └── utils.py           # Utility functions
├── images/                # Input images directory
│   └── image.png          # Sample image
├── output/                # Output results directory
│   └── session_*/         # Timestamped session folders
└── logs/                  # Application logs (auto-created)
```

## 🚀 Features

### Core Functionality
- **Text Detection & Recognition**: Powered by PaddleOCR 3.x
- **Multi-language Support**: Vietnamese (vi) and English (en)
- **Batch Processing**: Process single images or entire directories
- **Confidence Filtering**: Filter results by confidence threshold

### Visualization
- **Interactive Plots**: Matplotlib-based visualizations with bounding boxes
- **Annotated Images**: PIL-based image annotations
- **Summary Statistics**: Batch processing statistics and charts
- **Grid Display**: Multiple images in grid layout

### Output Management
- **Multiple Formats**: Save results as TXT, JSON, or CSV
- **Timestamped Sessions**: Organized output with session folders
- **Annotated Images**: Save images with bounding boxes drawn
- **Comprehensive Logging**: Detailed application logs

## 📦 Installation

### Prerequisites
```bash
pip install paddleocr pillow matplotlib numpy
```

### Additional Dependencies (if needed)
```bash
# For better font support
pip install fonttools

# For enhanced image processing
pip install opencv-python
```

## 🎯 Usage

### Command Line Interface

#### Process a single image:
```bash
python main.py images/image.png
```

#### Process all images in a directory:
```bash
python main.py images/
```

#### With custom options:
```bash
python main.py images/ --confidence 0.8 --lang en --no-display
```

#### Command line options:
- `--confidence`: Minimum confidence threshold (0.0-1.0, default: 0.5)
- `--lang`: OCR language (vi/en, default: vi)
- `--no-display`: Don't show visualizations
- `--no-save`: Don't save results to files

### Programmatic Usage

#### Basic Example:
```python
from main import OCRApplication

# Initialize application
app = OCRApplication()

# Process single image
result = app.process_single_image("images/image.png")
print(f"Found {result['text_regions_found']} text regions")
```

#### Custom Configuration:
```python
# Custom settings
config = {
    'ocr': {'lang': 'en', 'use_textline_orientation': True},
    'output': {'confidence_threshold': 0.8},
    'visualization': {'bbox_color': 'blue'}
}

app = OCRApplication(config)
result = app.process_directory("images/")
```

### Example Scripts

Run the provided examples:
```bash
python example_usage.py
```

## ⚙️ Configuration

### Main Configuration (config/settings.py)

#### OCR Settings:
```python
OCR_CONFIG = {
    "use_textline_orientation": True,
    "lang": "vi",  # Vietnamese language
    "use_gpu": False,
    "cpu_threads": 4
}
```

#### Visualization Settings:
```python
VISUALIZATION_CONFIG = {
    "figure_size": (12, 10),
    "bbox_color": "red",
    "text_fontsize": 8
}
```

#### Output Settings:
```python
OUTPUT_CONFIG = {
    "confidence_threshold": 0.5,
    "save_annotated_images": True,
    "text_output_format": "txt"  # txt, json, csv
}
```

## 📊 Output Examples

### Console Output:
```
=== OCR RESULTS FOR IMAGE.PNG ===
 1. Text: @taixaitech | Confidence: 1.00
 2. Text: Tham gia | Confidence: 1.00
 3. Text: So sánh CPU mới nhất của Intel với AMD và Qualcomm! | Confidence: 0.92
```

### Saved Files:
- `output/session_20241222_143022/image_annotated.png` - Image with bounding boxes
- `output/session_20241222_143022/image_results.txt` - Text results
- `output/session_20241222_143022/image_results.json` - JSON format results

## 🔧 Module Details

### OCRProcessor (`src/ocr_processor.py`)
- **OCRResult**: Container for OCR results with convenient access methods
- **OCRProcessor**: Main OCR processing using PaddleOCR
- **OCRBatchProcessor**: Batch processing with progress tracking

### Visualizer (`src/visualizer.py`)
- **Visualizer**: Create matplotlib visualizations and PIL annotations
- Methods: `visualize_results()`, `create_annotated_image()`, `create_results_summary_plot()`

### Utils (`src/utils.py`)
- **ImageHandler**: Image loading, validation, and basic operations
- **FileManager**: File operations and output management
- Helper functions for confidence filtering and result summaries

## 🐛 Troubleshooting

### Common Issues:

1. **Import Errors**: Ensure all dependencies are installed
2. **Font Issues**: Install system fonts or use default fonts
3. **Memory Issues**: Reduce image size or batch size for large datasets
4. **Language Support**: Verify PaddleOCR language models are downloaded

### Logging:
Check `logs/ocr_app.log` for detailed error information.

## 🔄 Migration from Old Code

The old `main.py` functionality is preserved but enhanced:

**Old way:**
```python
from paddleocr import PaddleOCR
ocr = PaddleOCR(use_textline_orientation=True, lang='vi')
result = ocr.predict('image.png')
```

**New way:**
```python
from main import OCRApplication
app = OCRApplication()
result = app.process_single_image('images/image.png')
```

## 📈 Performance Tips

1. **GPU Acceleration**: Set `use_gpu: True` in config if available
2. **Batch Processing**: Process multiple images together for efficiency
3. **Image Preprocessing**: Resize large images to reasonable dimensions
4. **Confidence Filtering**: Use appropriate thresholds to reduce noise

## 🤝 Contributing

1. Follow the modular structure when adding features
2. Update configuration in `config/settings.py`
3. Add comprehensive logging
4. Include examples in `example_usage.py`

## 📄 License

This project uses PaddleOCR which is licensed under Apache 2.0.
