"""
Configuration settings for the OCR application.
"""
from pathlib import Path

# Base paths
BASE_DIR = Path(__file__).parent.parent
IMAGES_DIR = BASE_DIR / "images"
OUTPUT_DIR = BASE_DIR / "output"
SRC_DIR = BASE_DIR / "src"

# OCR Configuration
OCR_CONFIG = {
    "use_textline_orientation": True,
    "lang": "vi",  # Vietnamese language support
}

# Visualization Configuration
VISUALIZATION_CONFIG = {
    "figure_size": (12, 10),
    "bbox_color": "red",
    "bbox_linewidth": 2,
    "text_color": "red",
    "text_fontsize": 8,
    "text_weight": "bold",
    "text_bbox_style": {
        "boxstyle": "round,pad=0.3",
        "facecolor": "white",
        "alpha": 0.8
    }
}

# Image Processing Configuration
IMAGE_CONFIG = {
    "supported_formats": [".png", ".jpg", ".jpeg", ".bmp", ".tiff", ".webp"],
    "output_format": "png",
    "max_image_size": (4000, 4000),  # Maximum image dimensions
    "quality": 95  # Output quality for saved images
}

# Output Configuration
OUTPUT_CONFIG = {
    "save_annotated_images": True,
    "save_text_results": True,
    "text_output_format": "txt",  # Options: txt, json, csv
    "confidence_threshold": 0.5,  # Minimum confidence to include in results
    "create_timestamp_folders": True
}

# Logging Configuration
LOGGING_CONFIG = {
    "level": "INFO",
    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    "log_to_file": True,
    "log_file": BASE_DIR / "logs" / "ocr_app.log"
}

def ensure_directories():
    """Ensure all required directories exist."""
    directories = [IMAGES_DIR, OUTPUT_DIR, SRC_DIR, LOGGING_CONFIG["log_file"].parent]
    for directory in directories:
        directory.mkdir(parents=True, exist_ok=True)

def get_config():
    """Get all configuration as a dictionary."""
    return {
        "paths": {
            "base_dir": BASE_DIR,
            "images_dir": IMAGES_DIR,
            "output_dir": OUTPUT_DIR,
            "src_dir": SRC_DIR
        },
        "ocr": OCR_CONFIG,
        "visualization": VISUALIZATION_CONFIG,
        "image": IMAGE_CONFIG,
        "output": OUTPUT_CONFIG,
        "logging": LOGGING_CONFIG
    }
